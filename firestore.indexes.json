{"indexes": [{"collectionGroup": "tripItineraries", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tripId", "order": "ASCENDING"}, {"fieldPath": "day", "order": "ASCENDING"}, {"fieldPath": "time", "order": "ASCENDING"}]}, {"collectionGroup": "tripItineraries", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tripId", "order": "ASCENDING"}, {"fieldPath": "day", "order": "ASCENDING"}, {"fieldPath": "time", "order": "ASCENDING"}]}, {"collectionGroup": "userTrips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "tripId", "order": "ASCENDING"}]}, {"collectionGroup": "members", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "joinedAt", "order": "DESCENDING"}]}, {"collectionGroup": "members", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "joinedAt", "order": "ASCENDING"}]}, {"collectionGroup": "squads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "squadId", "order": "ASCENDING"}, {"fieldPath": "joinedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tripSavings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "tripId", "order": "ASCENDING"}]}, {"collectionGroup": "invitations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inviteeId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "invitations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "squadId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "invitation-links", "queryScope": "COLLECTION", "fields": [{"fieldPath": "squadId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "invitationId", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "invitation-sends", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "sentAt", "order": "DESCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "subscriptionStatus", "order": "ASCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "subscriptionPlan", "order": "ASCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriptionStatus", "order": "ASCENDING"}, {"fieldPath": "subscriptionPlan", "order": "ASCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "precedence", "order": "ASCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "userSubscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "precedence", "order": "ASCENDING"}]}, {"collectionGroup": "userPreferences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "theme", "order": "ASCENDING"}]}, {"collectionGroup": "userPreferences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "userSubscriptions", "fieldPath": "subscriptionStatus", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userSubscriptions", "fieldPath": "subscriptionPlan", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userSubscriptions", "fieldPath": "subscriptionCurrentPeriodEnd", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userPreferences", "fieldPath": "userId", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userPreferences", "fieldPath": "theme", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userPreferences", "fieldPath": "travelPreferences", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "userPreferences", "fieldPath": "updatedAt", "indexes": [{"order": "DESCENDING", "queryScope": "COLLECTION"}]}]}